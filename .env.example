APP_DEBUG=true

#User-Agent浏览器标识
UA=xnhc-pay/5.5

REDIS_SCHEME=tcp
REDIS_HOST=host.docker.internal
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_TIMEOUT=30
REDIS_PERSISTENT=false

[APP]
DEFAULT_TIMEZONE=Asia/Shanghai
NAME=深圳新能智慧充电科技有限公司
VERSION=1.0.0
HOST=https://admin.resmartcharge.com/
ENV=production

[DATABASE]
TYPE=mysql

HOSTNAME=host.docker.internal
DATABASE=database
USERNAME=username
PASSWORD=
HOSTPORT=3306
CHARSET=utf8mb4
DEBUG=true

[LANG]
default_lang=zh-cn

[FILESYSTEM]
DRIVER=public

[CACHE]
DRIVER=redis

[MY]
WS_PORT=6871
API_HOST=http://127.0.0.1:6872
# 设备管理服务-充电桩的接口地址前缀
CHARGE_SERVICE_WEBMAN_API=http://127.0.0.1:9710/GetApi/
# 设备管理服务-消息中心的接口地址前缀
CHARGE_SERVICE_WEBMAN_NOTICE=http://127.0.0.1:9710/NoticeApi/
# 设备管理服务-能源路由器【就是集中控制器】的接口地址前缀
CHARGE_SERVICE_WEBMAN_CENTRALIZED_API=http://127.0.0.1:9710/CentralizedApi/
# 设备管理服务-令牌
CHARGE_SERVICE_WEBMAN_TOKEN=
HOST=https://api.resmartcharge.com/v1/admin/
QQ_MAP_KEY=
# 下发定时器的默认超时时间
SEND_HANDLE_TIMER_TIMEOUT=10
# 下发指令进程地址【客户端配置服务端地址时使用的】
SEND_HANDLE_TIMER_API=http://127.0.0.1:14440/
# 下发指令进程主机名【进程开启时监听的地址】
SEND_HANDLE_TIMER_HOST=http://0.0.0.0:14440
# 超时处理进程地址【客户端配置服务端地址时使用的】
TIMEOUT_HANDLE_JOB_API=Text://127.0.0.1:14450/
# 超时处理进程主机名【进程开启时监听的地址】
TIMEOUT_HANDLE_JOB_HOST=Text://0.0.0.0:14450
# 开始充电定时器进程地址【客户端配置服务端地址时使用的】
START_CHARGING_TIMER_API=Text://127.0.0.1:14470/
# 开始充电定时器进程主机名【进程开启时监听的地址】
START_CHARGING_TIMER_HOST=Text://0.0.0.0:14470
# 开始充电定时器进程地址【客户端配置服务端地址时使用的】
START_CHARGING_JOB_API=http://127.0.0.1:14460/
# 开始充电进程主机名【进程开启时监听的地址】
START_CHARGING_JOB_HOST=http://0.0.0.0:14460
# 充电中消息中心Socket地址
CHARGE_NOTICE_SOCKET_REMOTE_ADDRESS=ws://127.0.0.1:7001

# API网关地址
API_GATEWAY=http://host.docker.internal:10000

[WECHAT]
# 企业微信API密钥
QY_API_KEY=
# 内部API令牌【防止被外人调用】
INTERNAL_API_TOKEN=
# 微信支付APPID
APPID=wxa152187f4106e964
# 微信支付密钥
SECRET=
# API v3 密钥
API_KEY_V3=
# API v2 密钥
API_KEY_V2=
# 商户号
MCH_ID=1650543534
# 微信支付回调地址
WEIXIN_CALLBACK_URL=https://admin.resmartcharge.com/applet/Pay/weixin_callback
# 微信退款回调地址
WEIXIN_REFUND_CALLBACK_URL=https://admin.resmartcharge.com/applet/Pay/weixin_refund_callback
# 微信支付平台证书序列号
WEIXIN_PINGTAI_SN=
# 微信平台密钥
WEIXIN_PINGTAI_KEY=
#
API_CLIENT_KEY_SN=
# 微信支付公钥
API_CLIENT_PUBLIC_KEY[]=-----BEGIN PUBLIC KEY-----
API_CLIENT_PUBLIC_KEY[]=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtzb+sJLw7ewBD4GEilX2
API_CLIENT_PUBLIC_KEY[]=sBV4XE+hEeJ9pHrFTHEUB9eRYm9TWhyDsvEu0zwJUTOcErHyAOXNOGgj7cNVO5lb
API_CLIENT_PUBLIC_KEY[]=C4vEEFbaaeWwfvUg54IEnIhqQOdJqwvJOa64u96CY0rZHEuDUPxFwco8shuffXVd
API_CLIENT_PUBLIC_KEY[]=nsJ56UOX+aLvAS82+yq2GEmq1snmCquxlWflGbk9GyTK9pBvRpoGUVGWUWkMv36c
API_CLIENT_PUBLIC_KEY[]=ffaTchJkEhF7xrdyjHbc3TydW3V+qGACVPrI+iYdCrQwygkT8hVhEq523MbZ6pca
API_CLIENT_PUBLIC_KEY[]=N3/ChhQPyavnhMCavdRnfsfMWj6UWy4KL1+1AIh25awj1T19PfpFE8inWtFwuBCF
API_CLIENT_PUBLIC_KEY[]=9QIDAQAB
API_CLIENT_PUBLIC_KEY[]=-----END PUBLIC KEY-----
# 微信支付私钥
API_CLIENT_KEY[]=-----BEGIN PRIVATE KEY-----
API_CLIENT_KEY[]=xxx
API_CLIENT_KEY[]=xxx
API_CLIENT_KEY[]=xxx
API_CLIENT_KEY[]=-----END PRIVATE KEY-----
#
API_CLIENT_CERT[]=-----BEGIN CERTIFICATE-----
API_CLIENT_CERT[]=xxx
API_CLIENT_CERT[]=xxx
API_CLIENT_CERT[]=xxx
API_CLIENT_CERT[]=-----END CERTIFICATE-----

# 充电管理服务
[CHARGE_MANAGER_SERVICE]
# 服务地址
HOST=tcp://127.0.0.1:7273
# 连接超时时间
CONNECTION_TIMEOUT=1

[TRANSFER_SERVICE]
HOST=text://host.docker.internal:8383
DOMAIN=host.docker.internal:10000/v1/device
TOKEN=
APP_ID=
APP_SECRET=

# 运维小程序的配置
[MAINTENANCE_APPLET]
# 小程序APPID
APPID=wxc598fb1e4515f5a2
# 微信支付密钥
SECRET=

# 邮件
[EMAIL]
DEBUG=0
SMTP_HOST=smtp.exmail.qq.com
SMTP_PORT=465
SMTP_AUTH=true
USERNAME=
PASSWORD=
SMTP_SECURE=ssl
FROM_ADDRESS=
FROM_NAME=深圳新能智慧充电科技有限公司

# API 文档
[API_DOC]
PASSWORD=

# 调试接口
[DEBUG_API]
SECRET_KEY=

# 临时的告警配置
[TEMPORARY_ACTIVE_ALARM_CONFIG]
ENTERPRISE_WECHAT_KEY=
SF_IS_GENERATE_WORK_ORDER=2
SF_AUTO_DISPATCH_USER_ID=0
CSO_IS_GENERATE_WORK_ORDER=2
CSO_AUTO_DISPATCH_USER_ID=0
REMP_IS_GENERATE_WORK_ORDER=2
REMP_AUTO_DISPATCH_USER_ID=0
PO_IS_GENERATE_WORK_ORDER=2
PO_AUTO_DISPATCH_USER_ID=0
CCSR_IS_GENERATE_WORK_ORDER=2
CCSR_AUTO_DISPATCH_USER_ID=0
EMAIL=

# 小程序 Scoket 配置
[APPLET]
SOCKET_HOST=host.docker.internal
SOCKET_PORT=9202

# RabbitMQ 配置
[RABBITMQ]
HOST=127.0.0.1
PORT=5672
USER=guest
PASSWORD=guest
VHOST=/
CONNECTION_TIMEOUT=3.0
READ_WRITE_TIMEOUT=3.0
HEARTBEAT=0
KEEPALIVE=false
