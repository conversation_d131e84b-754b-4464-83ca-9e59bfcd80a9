<?php
/** @noinspection PhpUnused */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\lib\WeChatPay;
use app\common\logic\admin\refund_order\RefundSuccess;
use app\common\model\Users as UsersModel;
use app\common\traits\Curd;
use app\ms\Api;
use hg\apidoc\annotation as Apidoc;
use app\common\model\RefundOrder as RefundOrderModel;
use app\common\model\PayOrder as PayOrderModel;
use Respect\Validation\Exceptions\Exception;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\facade\Log;
use think\response\Json;
use Throwable;

#[Apidoc\Title("财务中心/退款订单")]
class RefundOrder extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new RefundOrderModel();
    }

    #[
        Apidoc\Title("退款订单列表"),
        Apidoc\Author("lwj 2024.2.21 新增，lwj 2024.6.13 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RefundOrder/refund_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Param(name: "user_id", type: "int", desc: "用户id"),
        Apidoc\Param(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Param(name: "out_refund_id", type: "string", desc: "外部退款号"),
        Apidoc\Param(name: "out_transaction_id", type: "string", desc: "外部交易号"),
        Apidoc\Param(name: "state", type: "array|int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Param(name: "time_range", type: "array", require: true, desc: "日期范围"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '退款流水号'],
            ['name' => 'order_id', 'type' => 'string', 'desc' => '充值订单号'],
            ['name' => 'out_refund_id', 'type' => 'string', 'desc' => '外部退款号'],
            ['name' => 'out_transaction_id', 'type' => 'string', 'desc' => '外部交易号'],
            ['name' => 'refund_price', 'type' => 'int', 'desc' => '退款金额'],
            ['name' => 'order_price', 'type' => 'int', 'desc' => '订单总金额'],
            ['name' => 'payer_refund', 'type' => 'int', 'desc' => '【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额'],
            ['name' => 'currency', 'type' => 'int', 'desc' => '币种：1:CNY,2:USD'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它'],
            ['name' => 'refund_screenshot_url', 'type' => 'string|null', 'desc' => '[新增]退款截图完整链接(部分历史数据与未退款成功的退款订单会返回NULL)'],

            ['name' => 'channel', 'type' => 'string', 'desc' => '退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡'],
            ['name' => 'out_status', 'type' => 'string', 'desc' => '微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常'],
            ['name' => 'out_notice_status', 'type' => 'string', 'desc' => '外部通知状态，退款状态，枚举值：，SUCCESS：退款成功，CLOSED：退款关闭，ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'success_time', 'type' => 'string', 'desc' => '退款成功时间'],

            ['name' => 'user_id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'nickname', 'type' => 'string', 'desc' => '用户昵称'],
            ['name' => 'phone', 'type' => 'string', 'desc' => '用户手机号'],
        ]),
    ]
    public function refund_order_list(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }
        $data = $this->request->post();
        // 将时间范围转为开始时间和结束时间
        if (!empty($data['time_range']) &&
            is_array($data['time_range']) &&
            count($data['time_range']) == 2
        ) {
            $data['start_time'] = $data['time_range'][0];
            $data['end_time'] = $data['time_range'][1];
        }
        unset($data['time_range']);
        // 将数组转换成字符串
        $this->processArrayParam($data, 'state');

        // 改为调用微服务
        try {
            $res = Api::send('/order/pay/refund', 'GET', $data);
            $result['total'] = $res['data']['count'] ?? 0;
            $result['data'] = $res['data']['list'] ?? [];

            return $this->res_success($result, $res['msg'], $res['code']);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("获取退款订单排序信息"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/RefundOrder/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "string", desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "string", desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_list", type: "array", desc: "排序列表", children: [
            ['name' => 'value', 'type' => 'string', 'desc' => '排序字段'],
            ['name' => 'label', 'type' => 'string', 'desc' => '排序字段名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        $res = [
            'order_name' => 'a.create_time',
            'order_type' => 'desc',
            'sort_list' => [
                ['value' => 'a.create_time', 'label' => '创建时间'],
//                ['value' => 'a.id', 'label' => '退款流水号'],
//                ['value' => 'a.user_id', 'label' => '用户id'],
//                ['value' => 'a.state', 'label' => '状态'],
//                ['value' => 'a.success_time', 'label' => '退款成功时间'],
//                ['value' => 'a.out_status', 'label' => '外部申请返回状态'],
//                ['value' => 'a.out_notice_status', 'label' => '外部通知状态'],
            ],
        ];
        return $this->res_success($res);
    }

    #[
        Apidoc\Title("退款订单详情"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RefundOrder/get_info"),
        Apidoc\Param(name: "refund_id", type: "string", require: true, desc: "退款流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Returned(name: "out_refund_id", type: "string", desc: "外部退款号"),
        Apidoc\Returned(name: "out_transaction_id", type: "string", desc: "外部交易号"),
        Apidoc\Returned(name: "refund_price", type: "int", desc: "退款金额"),
        Apidoc\Returned(name: "order_price", type: "int", desc: "订单总金额"),
        Apidoc\Returned(name: "payer_refund", type: "int", desc: "【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Returned(name: "channel", type: "string", desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
        Apidoc\Returned(name: "out_status", type: "string", desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "out_notice_status", type: "string", desc: "外部通知状态，退款状态，枚举值：，SUCCESS：退款成功，CLOSED：退款关闭，ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "创建时间"),
        Apidoc\Returned(name: "success_time", type: "string", desc: "退款成功时间"),
        Apidoc\Returned(name: "user_id", type: "int", desc: "用户id"),
        Apidoc\Returned(name: "nickname", type: "string", desc: "用户昵称"),
        Apidoc\Returned(name: "phone", type: "string", desc: "用户手机号"),
        Apidoc\Returned(name: "callback_list", type: "array", desc: "回调列表"),

        Apidoc\Returned(name: "ip", type: "string", desc: "客户ip"),
        Apidoc\Returned(name: "msg", type: "string", desc: "结果信息"),
        Apidoc\Returned(name: "user_received_account", type: "string", desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
        Apidoc\Returned(name: "refund_screenshot_url", type: "string|null", desc: "[新增]退款截图完整链接(部分历史数据与未退款成功的退款订单会返回NULL)")
    ]
    public function get_info(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        $data = $this->request->post();
        $verify_data = v::input($data, VerifyData::pay_order([
            'refund_id',
        ]));
        $data['id'] = $verify_data['refund_id'];

        // 改为调用微服务
        try {
            $res = Api::send('/order/pay/refund/info', 'GET', $data);
            return $this->res_success($res['data'], $res['msg'], $res['code']);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("[新增]退款成功"),
        Apidoc\Author("cbj 2024.11.19 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RefundOrder/refund_success"),
        Apidoc\Param(name: "refund_id", type: "string", require: true, desc: "退款流水号"),
        Apidoc\Param(name: "refund_screenshot_id", type: "int", require: true, desc: "退款截图ID(请使用公共上传图片接口上传截图)"),
    ]
    public function refund_success(): Json
    {
        $refund_id = $this->request->post('refund_id');
        if (empty($refund_id)) {
            return $this->res_error([], '退款流水号不能为空');
        }

        return $this->openExceptionCatch(function () {

            return (new RefundSuccess($this->loginUser, $this->request))->run();

        }, true, sprintf("lock_key_refund_success_%s", $refund_id));
    }

    #[
        Apidoc\Title("同意并退款(已废弃)"),
        Apidoc\Author("lwj 2024.2.21 新增，lwj 2024.4.2 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RefundOrder/agree_and_refund"),
        Apidoc\Param(name: "refund_id", type: "string", require: true, desc: "退款流水号"),

        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "refund_price", type: "int", require: true, desc: "退款金额，单位分"),
    ]
    public function agree_and_refund(): Json
    {
        return $this->res_error([], '已废弃');
//
//        if ($this->loginUser->corp_id > 0) {
//            return $this->res_error([], '没有权限');
//        }
//
//        try {
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::pay_order([
//                'refund_id',
//            ]));
//
//            $refund_cache = cache('后台退款操作_' . $verify_data['refund_id']);
//            if ($refund_cache) return $this->res_error([], '同一退款订单，请勿频繁操作');
//            cache('后台退款操作_' . $verify_data['refund_id'], 1, 4);
//
//            $refund_order = $this->modelClass
//                ->where('id', $verify_data['refund_id'])
//                ->field('id,order_id,refund_price,order_price,currency,state')
//                ->find();
//            if (!$refund_order) return $this->res_error([], '退款订单不存在');
//
//            return $this->openExceptionCatch(function () use ($refund_order) {
//
//                $res_refund_order = $this->modelClass
//                    ->where('id', $refund_order['id'])
//                    ->where('state', 1)
//                    ->limit(1)
//                    ->update([
//                        'state' => 5,
//                        'msg' => DB::raw("CONCAT(IFNULL(msg, ''), '已同意并退款；')"),
//                    ]);
//                if (!$res_refund_order) throw new RuntimeException(
//                    '退款失败',
//                    [],
//                    RuntimeException::CodeBusinessException
//                );
//
//                $WeChatPay = new WeChatPay;
//                $res_refund = $WeChatPay->order_refund(
//                    $refund_order->order_id,
//                    $refund_order->id,
//                    $refund_order->refund_price,
//                    $refund_order->order_price,
//                );
//                if (empty($res_refund['status'])) {
//                    $message = $res_refund['message'] ?? '未知错误';
//
//                    $res_refund_order = $this->modelClass
//                        ->where('id', $refund_order['id'])
//                        ->where('state', 5)
//                        ->limit(1)
//                        ->update([
//                            'state' => 30,
//                            'msg' => DB::raw("CONCAT(IFNULL(msg, ''), '退款失败:" . $message . "；')"),
//                        ]);
//                    trace('同意并退款=》退款失败，更新退款订单结果：' . json_encode_cn($res_refund_order), '信息');
//
//                    $res_pay_order = (new PayOrderModel)
//                        ->where('id', $refund_order['order_id'])
//                        ->where('state', 2)
//                        ->limit(1)
//                        ->update([
//                            'refund_state' => 4,
//                        ]);
//                    trace('同意并退款=》退款失败，更新支付订单结果：' . json_encode_cn($res_pay_order), '信息');
//                    throw new RuntimeException(
//                        '退款失败:' . $message,
//                        [],
//                        RuntimeException::CodeBusinessException
//                    );
////                    return $this->res_error([], '退款失败');
//                }
//
//                $res_refund_order = $this->modelClass
//                    ->where('id', $refund_order['id'])
//                    ->limit(1)
//                    ->update([
//                        'out_refund_id' => $res_refund['refund_id'],
//                        'channel' => $res_refund['channel'],
//                        'user_received_account' => $res_refund['user_received_account'],
//                        'out_status' => $res_refund['status'],
//                        'msg' => DB::raw("CONCAT(IFNULL(msg, ''), ' 退款接口返回:', '" . $res_refund['status'] . "；')"),
//                    ]);
//                trace('同意并退款=》更新结果：' . json_encode_cn($res_refund_order), '信息');
//
//                if (in_array($res_refund['status'], ['SUCCESS', 'PROCESSING'])) {
//                    return $this->res_success([
//                        'channel' => $res_refund['channel'],
//                        'user_received_account' => $res_refund['user_received_account'],
//                        'out_status' => $res_refund['status'],
//                        'refund_price' => $refund_order->refund_price,
//                    ], '提交退款成功，请等待微信处理', is_open_exception_catch: true);
//                } else {
//                    return $this->res_warning([
//                        'channel' => $res_refund['channel'],
//                        'user_received_account' => $res_refund['user_received_account'],
//                        'out_status' => $res_refund['status'],
//                        'refund_price' => $refund_order->refund_price,
//                    ], '退款异常', is_open_exception_catch: true);
//                }
//
//            }, false, sprintf("lock_key_%s", $verify_data['refund_id']));
//
//        } catch (ValidationException $e) {
//            return $this->res_error([], $e->getMessage());
//        } catch (Throwable $e) {
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
    }

    #[
        Apidoc\Title("拒绝退款"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/RefundOrder/refuse_refund"),
        Apidoc\Param(name: 'refund_id', type: "string", require: true, desc: '退款流水号'),

        Apidoc\Returned(name: "refund_price", type: "int", require: true, desc: "退款金额，单位分"),
    ]
    public function refuse_refund(): Json
    {
        if ($this->loginUser->corp_id > 0) {
            return $this->res_error([], '没有权限');
        }

        try {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'refund_id',
            ]));

            $refund_cache = cache('后台退款操作_' . $verify_data['refund_id']);
            if ($refund_cache) return $this->res_error([], '同一退款订单，请勿频繁操作');
            cache('后台退款操作_' . $verify_data['refund_id'], 1, 4);

            $refund_order = $this->modelClass
                ->where('id', $verify_data['refund_id'])
                ->field('id,user_id,order_id,refund_price,order_price,currency,state')
                ->find();
            if (!$refund_order) return $this->res_error([], '退款订单不存在');
            if ($refund_order['state'] !== 1) return $this->res_error([], '该笔退款正在处理或已处理，不能取消，如有疑问，请查看日志');

            return $this->openExceptionCatch(function () use ($refund_order) {

                $res_refund_order = $this->modelClass
                    ->where('id', $refund_order['id'])
                    ->where('state', 1)
                    ->limit(1)
                    ->update([
                        'state' => 10,
                        'msg' => DB::raw("CONCAT(IFNULL(msg, ''), '拒绝退款；')"),
                    ]);
                if (!$res_refund_order) throw new RuntimeException(
                    '拒绝失败，更新退款订单失败，请检查订单状态',
                    [],
                    RuntimeException::CodeBusinessException
                );

                $res_pay_order = (new PayOrderModel)
                    ->where('id', $refund_order['order_id'])
                    ->where('refund_state', 2)
                    ->limit(1)
                    ->update([
                        'refund_state' => 10,
                    ]);
                trace('拒绝退款=》更新充值订单结果：' . json_encode_cn($res_pay_order), '信息');
                if (!$res_pay_order) throw new RuntimeException(
                    '拒绝失败，更新充值订单失败，请检查订单状态',
                    [],
                    RuntimeException::CodeBusinessException
                );

                $add_balance_res = (new UsersModel)
                    ->where('id', $refund_order['user_id'])
                    ->limit(1)
                    ->update([
                        'balance' => Db::raw('balance+' . $refund_order['refund_price']),
                    ]);
                if (!$add_balance_res) throw new RuntimeException(
                    '拒绝退款失败，加余额失败',
                    [],
                    RuntimeException::CodeBusinessException
                );
                return $this->res_success([
                    'refund_price' => $refund_order['refund_price'],
                ], '拒绝退款成功', is_open_exception_catch: true);

            }, true, sprintf("lock_key_%s", $verify_data['refund_id']));
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


}