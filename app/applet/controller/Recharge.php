<?php
/** @noinspection PhpUnused */
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpInapplicableAttributeTargetDeclarationInspection */

namespace app\applet\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\rabbitmq\Publisher;
use app\common\lib\VerifyData;
use app\common\lib\WeChatPay;
use app\common\model\RechargeList as RechargeListModel;
use app\common\traits\Curd;
use app\ms\Api;
use Exception;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use app\common\model\PayOrder as PayOrderModel;
use app\common\model\Order as ChargeOrderModel;
use app\common\model\Users as UsersModel;
use app\common\model\RefundOrder as RefundOrderModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\facade\Log;
use think\response\Json;
use Throwable;

#[Apidoc\Title("充值")]
class Recharge extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new PayOrderModel();
    }

    #[
        Apidoc\Title("充值列表"),
        Apidoc\Author("lwj 2023.8.14 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/applet/Recharge/list"),
        Apidoc\NotResponseSuccess,
        Apidoc\Returned(name: "code", type: "int", require: true, default: 200, desc: "返回码，200"),
        Apidoc\Returned(name: "msg", type: "string", require: true, default: "成功", desc: "返回描述"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "运营商列表", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'id'],
            ['name' => 'title', 'type' => 'string', 'desc' => '标题'],
            ['name' => 'original_price', 'type' => 'string', 'desc' => '原价'],
            ['name' => 'price', 'type' => 'string', 'desc' => '支付金额'],
            ['name' => 'currency', 'type' => 'string', 'desc' => '币种：1:CNY,2:USD'],
        ]),
    ]
    public function list(): Json
    {
        try {
            $res = Api::send('/order/pay/price', 'GET');
            return $this->res_success($res['data'], $res['msg'], $res['code']);
        }catch (Throwable $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("创建充值订单"),
        Apidoc\Author("lwj 2023.8.14 新增，lwj 2023.8.26 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/create_order"),
        Apidoc\Param(name: 'id', type: "int", require: true, desc: '充值列表id'),
        Apidoc\Param(name: 'price', type: "int", require: true, desc: '支付金额'),
    ]
    public function create_order(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'id',
                'price',
            ]));
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
        $data  = [
            'price_id' => $verify_data['id'],
            'user_id' => $user->id,
            'open_id' => $user->open_id,
            'price' => $verify_data['price'],
            'ip' => get_ip()
        ];
        try {
            $res = Api::send('/order/pay', 'POST', $data, [], '', false, true);
            return $this->res_success($res['data'], $res['msg'], $res['code']);
        }catch (Throwable $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("获取充值订单列表"),
        Apidoc\Author("lwj 2023.8.28 新增，lwj 2024.2.20 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_pay_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "支付流水号"),
        Apidoc\Param(name: "trade_no", type: "string", desc: "外部订单编号"),
        Apidoc\Param(name: "status", type: "array|int", desc: "状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '支付流水号'],
            ['name' => 'trade_no', 'type' => 'string', 'desc' => '外部订单编号'],
            ['name' => 'price', 'type' => 'int', 'desc' => '支付金额'],
            ['name' => 'currency', 'type' => 'int', 'desc' => '币种：1:CNY,2:USD'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付'],
            ['name' => 'add_balance', 'type' => 'int', 'desc' => '加余额结果，1成功，2失败，3没有加币（默认3）'],
            ['name' => 'type', 'type' => 'int', 'desc' => '支付类型，1充值余额，2充卡'],
            ['name' => 'refund_state', 'type' => 'int', 'desc' => '退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消'],
        ]),
    ]
    public function get_pay_order_list(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;

            $res = Api::send("/order/pay","GET",[
                'page' => $page,
                'limit' => $pageSize,
                "id" => $data['id'] ?? '',
                "trade_no" => $data['trade_no'] ?? '',
                'user_id' => $user->id,
                "state" => $data['state'] ?? '',
                "start_time" => $data['start_time'] ?? '',
                "end_time" => $data['end_time'] ?? '',
            ]);
            $result['total'] = $res['data']['count'] ?? 0;
            $result['data'] = $res['data']['list'] ?? [];

            return $this->res_success($result, $res['msg'], $res['code']);

        } catch (Throwable $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("获取充值详情"),
        Apidoc\Author("lwj 2024.8.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_pay_order_info"),
        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "充值订单号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "trade_no", type: "string", desc: "外部订单编号"),
        Apidoc\Returned(name: "name", type: "string", desc: "支付项名称"),
        Apidoc\Returned(name: "price", type: "int", desc: "支付金额"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1生成订单，2支付中，3完成支付，4失败或其他问题，5关闭支付"),
        Apidoc\Returned(name: "add_balance", type: "int", desc: "加余额结果，1成功，2失败，3没有加币（默认3）"),
        Apidoc\Returned(name: "type", type: "int", desc: "支付类型，1充值余额，2充卡"),
        Apidoc\Returned(name: "refund_state", type: "int", desc: "退款状态，1无退款，2申请退款，3已退款，4退款失败，10已拒绝，11已取消"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "充值时间"),

    ]
    public function get_pay_order_info(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::pay_order([
                'order_id',
            ]));

            $res = Api::send("/order/pay/info","GET",[
                'id' => $verify_data['order_id'],
                'user_id' => $user->id,
            ]);
            return $this->res_success($res['data'], $res['msg'], $res['code']);
        } catch (Throwable $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    #[
        Apidoc\Title("退款"),
        Apidoc\Author("lwj 2024.2.19 新增，lwj 2024.7.15 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/refund"),
        Apidoc\Param(name: 'order_id', type: "string", require: true, desc: '支付订单id'),

//        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
//        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
//        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "refund_price", type: "int", require: true, desc: "退款金额，单位分"),
    ]
    public function refund(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'order_id',
            ]));
            $user_req = $user->toArray();
            $user_req['phone'] = (string)$user->phone;

            $UsersModel = new UsersModel;

            // 余额相关的校验还在旧项目实现
            $user2 = $UsersModel->where('id', $user->id)->field('id,balance')->find();
            if (!$user2) return $this->res_error([], '异常用户');
            if ($user2['balance']<=0) return $this->res_error([], '没有可退余额');

            // 调用订单服务校验该订单是否符合退款规则
            try {
                $res = Api::send("/order/pay/refund", "POST", [
                    'order_id' => $verify_data['order_id'],
                    'user' => $user_req,
                ], ['Authorization' => 'mpQWnQ0zZ7KGl3JEV5DeUoeNXec0mohT']);
                if ($res['code'] !== 200) return $this->res_error([], $res['msg']);
            }catch (Exception $e) {
                Log::error($e->getMessage());
                return $this->res_error([], '服务器异常,稍后再试');
            }

            Db::startTrans();
            try {
                $refund_price = min($user2['balance'], $res['data']);
                $deduction_balance_res = $UsersModel
                    ->where('id', $user->id)
                    ->where('balance', '>=', $refund_price)
                    ->limit(1)
                    ->update([
                        'balance' => Db::raw('balance-' . $refund_price),
                    ]);
                if(!$deduction_balance_res) throw new Exception('扣除余额失败');

                $user3 = $UsersModel
                    ->where('id', $user->id)
                    ->field('id,balance')
                    ->find();
                if(!$user3['balance']<0) throw new Exception('余额不足');

                // 扣减余额成功,发布消息等待退款订单创建
                Publisher::quickPublish([
                    'exchange' => 'order',
                    'type' => 'direct',
                    'queues' => ['order_sync'],
                    'routing_key' => 'order.sync',
                    'message' => [
                        'action' => 'refund',
                        'data' => [
                            'order_id' => $verify_data['order_id'],
                            'user_id' => $user->id,
                            'price' => $refund_price,
                            'ip' => get_ip()
                        ],
                    ]
                ]);

                // 提交事务
                Db::commit();
                return $this->res_success(['refund_price'=>$refund_price], '已提交退款申请，请耐心等待审核并退款，一般2-3个工作日处理，如有疑问，请联系客服处理');
            } catch (Exception $e) {
                // 回滚事务
                Db::rollback();
                return $this->res_error([], $e->getMessage());
            }
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取退款详情(最后一笔)"),
        Apidoc\Author("lwj 2024.2.20 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_refund_order_info_last"),
        Apidoc\Param(name: "order_id", type: "string", require: true, desc: "交易流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Returned(name: "out_refund_id", type: "string", desc: "外部退款号"),
        Apidoc\Returned(name: "out_transaction_id", type: "string", desc: "外部交易号"),
        Apidoc\Returned(name: "refund_price", type: "int", desc: "退款金额，单位为分"),
        Apidoc\Returned(name: "order_price", type: "int", desc: "订单总金额，单位为分"),
        Apidoc\Returned(name: "payer_refund", type: "int", desc: "【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Returned(name: "state_arr", type: "array", desc: "状态数组"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "申请退款时间"),
        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "out_notice_status", type: "string", desc: "外部通知状态，退款状态，枚举值：，SUCCESS：退款成功，CLOSED：退款关闭，ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款"),

    ]
    public function get_refund_order_info_last(): Json
    {
        try {
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::pay_order([
                'order_id',
            ]));

            try {
                $res = Api::send("/order/pay/refund/info", "GET", [
                    'order_id' => $verify_data['order_id'],
                ]);
            }catch (Exception $e) {
                Log::error($e->getMessage());
                return $this->res_error([], '服务器异常,稍后再试');
            }
            if (in_array($res['data']['state'], [1,5,20])){
                $res['data']['state_arr'] = [
                    ['key' => 1, 'name' => '已申请'],
                    ['key' => 5, 'name' => '已审核'],
                    ['key' => 20, 'name' => '已退款'],
                ];
            }
            if ($res['data']['state'] == 10){
                $res['data']['state_arr'] = [
                    ['key' => 1, 'name' => '已申请'],
                    ['key' => 10, 'name' => '已拒绝'],
                ];
            }else if($res['data']['state'] == 11){
                $res['data']['state_arr'] = [
                    ['key' => 1, 'name' => '已申请'],
                    ['key' => 11, 'name' => '已取消'],
                ];
            }else{
                $res['data']['state_arr'] = [
                    ['key' => 1, 'name' => '已申请'],
                    ['key' => 5, 'name' => '已审核'],
                    ['key' => $res['data']['state'], 'name' => '退款失败'],
                ];
            }

            return $this->res_success($res['data'], $res['msg'], $res['code']);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("取消退款"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/cancel_refund"),
        Apidoc\Param(name: 'refund_id', type: "string", require: true, desc: '退款流水号'),

        Apidoc\Returned(name: "refund_price", type: "int", require: true, desc: "退款金额，单位分"),
    ]
    public function cancel_refund(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::pay_order([
                'refund_id',
            ]));

            return $this->openExceptionCatch(function ()use($verify_data,$user) {
                $user_req = $user->toArray();
                $user_req['phone'] = (string)$user->phone;

                $res = Api::send("/order/pay/refund/cancel","POST",[
                    'refund_id' => $verify_data['refund_id'],
                    'user' => $user_req,
                ],['Authorization' => 'mpQWnQ0zZ7KGl3JEV5DeUoeNXec0mohT']);
                if($res['code'] != 200) throw new RuntimeException($res['msg'], [], RuntimeException::CodeBusinessException);

                $add_balance_res = (new UsersModel)
                    ->where('id', $user->id)
                    ->limit(1)
                    ->update([
                        'balance' => Db::raw('balance+' . $res['data']),
                    ]);
                if (!$add_balance_res){
                    Log::error('取消退款失败，加余额失败');
                    throw new RuntimeException('取消退款失败，加余额失败', [], RuntimeException::CodeBusinessException);
                }
                return $this->res_success(['refund_price' => $res['data'],], '取消退款成功',is_open_exception_catch:true);

            }, true, sprintf("lock_key_%s", $verify_data['refund_id']));
        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

/*
 * 没啥用 小程序没有对应的入口
 *     #[
        Apidoc\Title("获取退款订单列表"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_refund_order_list"),
        Apidoc\Param(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Param(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Param(name: "state", type: "array|int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Param(name: "start_time", type: "string", desc: "开始时间"),
        Apidoc\Param(name: "end_time", type: "string", desc: "结束时间"),

        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页显示"),
        Apidoc\Param(name: "order_name", type: "string", require: true, desc: "排序字段"),
        Apidoc\Param(name: "order_type", type: "string", require: true, desc: "排序类型"),

        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),

        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'string', 'desc' => '退款流水号'],
            ['name' => 'order_id', 'type' => 'string', 'desc' => '充值订单号'],
            ['name' => 'refund_price', 'type' => 'int', 'desc' => '退款金额'],
            ['name' => 'order_price', 'type' => 'int', 'desc' => '订单总金额'],
            ['name' => 'currency', 'type' => 'int', 'desc' => '币种：1:CNY,2:USD'],
            ['name' => 'state', 'type' => 'int', 'desc' => '状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它'],
            ['name' => 'create_time', 'type' => 'int', 'desc' => '申请时间'],
        ]),
    ]
    public function get_refund_order_list(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();
            $page = ($data['page'] ?? false) ?: 1;
            $pageSize = ($data['limit'] ?? false) ?: $this->pageSize;
            $order_name = ($data['order_name'] ?? false) ?: 'create_time';
            $order_type = ($data['order_type'] ?? false) ?: 'desc';

            // 搜索框搜索
            $where = function ($query) use ($data) {
                if (isset($data['id']) && $data['id']) {
                    $query->where('id', $data['id']);
                }
                if (isset($data['order_id']) && $data['order_id']) {
                    $query->where('order_id', $data['order_id']);
                }
                if (isset($data['state']) && $data['state']) {
                    if (is_array($data['state'])) {
                        $query->where('state', 'in', $data['state']);
                    } else {
                        $query->where('state', $data['state']);
                    }
                }

                if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
                    $query->where('create_time >= "' . $params['start_time'] . ' 00:00:00"');
                    $query->where('create_time <= "' . $params['end_time'] . ' 23:59:59"');
                }

                return $query;
            };

            $list = (new RefundOrderModel)
                ->field('id,order_id,refund_price,order_price,currency,state,create_time')
                ->where('user_id', $user->id)
                ->where($where)
                ->order($order_name, $order_type)
                ->paginate(['list_rows' => $pageSize, 'page' => $page]);

            return $this->res_success($list);

        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }

    #[
        Apidoc\Title("获取退款详情"),
        Apidoc\Author("lwj 2024.2.21 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Recharge/get_refund_order_info"),
        Apidoc\Param(name: "refund_id", type: "string", require: true, desc: "退款流水号"),

        Apidoc\Returned(name: "id", type: "string", desc: "退款流水号"),
        Apidoc\Returned(name: "order_id", type: "string", desc: "充值订单号"),
        Apidoc\Returned(name: "out_refund_id", type: "string", desc: "外部退款号"),
        Apidoc\Returned(name: "out_transaction_id", type: "string", desc: "外部交易号"),
        Apidoc\Returned(name: "refund_price", type: "int", desc: "退款金额，单位为分"),
        Apidoc\Returned(name: "order_price", type: "int", desc: "订单总金额，单位为分"),
        Apidoc\Returned(name: "payer_refund", type: "int", desc: "【用户退款金额】 退款给用户的金额，单位为分，不包含所有优惠券金额"),
        Apidoc\Returned(name: "state", type: "int", desc: "状态：1已申请，5退款中，10已拒绝，11已取消，20已退款，30退款失败，40其它"),
        Apidoc\Returned(name: "state_arr", type: "array", desc: "状态数组"),
        Apidoc\Returned(name: "currency", type: "int", desc: "币种：1:CNY,2:USD"),
        Apidoc\Returned(name: "create_time", type: "string", desc: "申请退款时间"),
        Apidoc\Returned(name: "channel", type: "string", require: true, desc: "退款渠道，ORIGINAL: 原路退款，BALANCE: 退回到余额，OTHER_BALANCE: 原账户异常退到其他余额账户，OTHER_BANKCARD: 原银行卡异常退到其他银行卡"),
        Apidoc\Returned(name: "user_received_account", type: "string", require: true, desc: "退款入账账户，1）退回银行卡：{银行名称}{卡类型}{卡尾号}，2）退回支付用户零钱:支付用户零钱，3）退还商户:商户基本账户商户结算银行账户，4）退回支付用户零钱通:支付用户零钱通"),
        Apidoc\Returned(name: "out_status", type: "string", require: true, desc: "微信返回状态（仅代表业务的受理情况，不代表退款成功到账）：SUCCESS: 退款成功，CLOSED: 退款关闭，PROCESSING: 退款处理中，ABNORMAL: 退款异常"),
        Apidoc\Returned(name: "out_notice_status", type: "string", desc: "外部通知状态，退款状态，枚举值：，SUCCESS：退款成功，CLOSED：退款关闭，ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款"),

    ]
    public function get_refund_order_info(): Json
    {
        try {
            $user = $this->loginUser;
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::pay_order([
                'refund_id',
            ]));

            $field = [
                'id',
                'order_id',
                'out_refund_id',
                'out_transaction_id',
                'refund_price',
                'order_price',
                'payer_refund',
                'state',
                'currency',
                'create_time',
                'channel',
                'user_received_account',
                'out_status',
                'out_notice_status',
            ];

            $info = (new RefundOrderModel)
                ->field($field)
                ->append(['state_arr'])
                ->where('id', $verify_data['refund_id'])
                ->where('user_id', $user->id)
                ->withAttr('state_arr', function ($value, $data) {
                    return match ($data['state']) {
                        1, 5, 20 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 5, 'name' => '已审核'],
                            ['key' => 20, 'name' => '已退款'],
                        ],
                        10 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 10, 'name' => '已拒绝'],
                        ],
                        11 => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 11, 'name' => '已取消'],
                        ],
                        default => [
                            ['key' => 1, 'name' => '已申请'],
                            ['key' => 5, 'name' => '退款中'],
                            ['key' => $data['state'], 'name' => '退款失败'],
                        ],
                    };
                })
                ->find();
            if (!$info) return $this->res_error([], '退款订单不存在');
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }
*/





//    /**
//     * 微信签名
//     * lwj 2021.8.28新增
//     */
//    public function get_Sign($url, $http_method, $body, $sign_key, $merchant_id, $serial_no)
//    {
//        $timestamp     = time();//时间戳
//        $nonce         = $timestamp . rand(10000, 99999);//随机字符串
//        $url_parts     = parse_url($url);
//        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?${url_parts['query']}" : ""));
//        $message       =
//            $http_method . "\n" .
//            $canonical_url . "\n" .
//            $timestamp . "\n" .
//            $nonce . "\n" .
//            $body . "\n";
//        openssl_sign($message, $raw_sign, $sign_key, 'sha256WithRSAEncryption');
//        $sign  = base64_encode($raw_sign);
//        $token = sprintf('mchid="%s",nonce_str="%s",timestamp="%d",serial_no="%s",signature="%s"',
//            $merchant_id, $nonce, $timestamp, $serial_no, $sign);
//        return $token;
//    }
//
//    /**
//     * 获取平台证书内容
//     * lwj 2021.8.28新增
//     */
//    public function get_Certificates($merchant_id, $serial_no, $Key)
//    {
//        $sign     = $this->get_Sign("https://api.mch.weixin.qq.com/v3/certificates", "GET", "", $Key, $merchant_id, $serial_no);//$http_method要大写
//        $header[] = 'User-Agent:' . env('ua');
//        $header[] = 'Accept:application/json';
//        $header[] = 'Authorization:WECHATPAY2-SHA256-RSA2048 ' . $sign;
//        $back     = get_http_data("https://api.mch.weixin.qq.com/v3/certificates", $header);
//        return $back;
//    }
//
//
//    public function ttt()
//    {
//        return $this->get_Certificates(config('wechat.mch_id'), '37FAAC15E804AB9E03D3F69F658BC10CE4E3E389', config('wechat.apiclient_key'));
//    }

//    public function hdf()
//    {
//
//        $decrypter = new AesUtil($opts['key']);
//        $plain = $decrypter->decryptToString($encCert['associated_data'], $encCert['nonce'], $encCert['ciphertext']);
//        $plain = AesGcm::decrypt($encCert['ciphertext'], $opts['key'], $encCert['nonce'], $encCert['associated_data']);
//    }


//    #[
//        Apidoc\Title("充值记录"),
//        Apidoc\Author("swk 2023.7.25 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/applet/recharge/recharge_record"),
//        Apidoc\Param(name: 'page', require: true, desc: '页数'),
//        Apidoc\Param(name: 'limit', require: true, desc: '记录数')
//    ]
//    public function recharge_record()
//    {
//        $user_id = $this->request->token_user['id'];
//        $page    = $this->request->post('page') ?? 1;
//        $limit   = $this->request->post('limit') ?? 15;
//        $res     = $this->recharge_order_model->where('user_id', $user_id)->where('is_pay', 1)->paginate(['list_rows' => $page, 'page' => $limit]);
//        $this->res_success($res);
//    }


    /**
     * 获取充值订单编号
     * lwj 2023.8.14 新增
     * @return string
     */
    public function get_order_no(): string
    {
        $maxRetries = 5;
        $retryCount = 0;
        $datePrefix = date('ymd'); // 生成6位日期前缀

        while ($retryCount < $maxRetries) {
            $randomPart = bin2hex(random_bytes(13)); // 26位随机字符串
            $order_no = $datePrefix . $randomPart; // 合并成32位订单号

            $exist_order_no = $this->modelClass->field('id')->find($order_no);
            if (!$exist_order_no) {
                return $order_no;
            }

            $retryCount++;
        }

        throw new RuntimeException('生成订单号失败，请重试');
    }

    /**
     * 获取退款编号
     * lwj 2024.2.19 新增
     * lwj 2024.2.20 修改
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function get_refund_id(): string
    {
        $RefundOrder = new RefundOrderModel;
        while (true) {
            $refund_id = 'T'.date('Ymd').'M'.get_microtime().'S'.mt_rand(10000, 99999);
            if (strlen($refund_id) > 64) continue;
            $exist_refund_id = $RefundOrder->field('id')->find($refund_id);
            if (!$exist_refund_id) {
                break;
            }
        }
        return $refund_id;
    }
}