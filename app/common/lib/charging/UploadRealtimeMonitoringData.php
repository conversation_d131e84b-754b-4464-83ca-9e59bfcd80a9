<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\common\lib\charging;

use app\common\cache\base\ChargingContextData;
use app\common\context\Order;
use app\common\lib\charging\request\UploadRealtimeMonitoringDataRequest;
use app\common\lib\charging\sdk\request\StopChargeCommandRequest;
use app\common\lib\charging\sdk\StopChargeCommand;
use app\common\lib\rabbitmq\Publisher;
use app\common\lib\SendAdmin;
use app\common\lib\WeChat;
use app\common\log\SocketLogCollector;
use app\common\model\Order as OrderModel;
use app\common\model\Stations;
use app\common\model\TariffGroup;
use app\common\model\Users;
use app\event\DeviceAbnormalityEvent;
use app\ms\Api;
use LieHuoHuYu\ChargeTransferServiceProtocol\contract\package\operate\EndOrder;
use Predis\Client as Predis;
use RuntimeException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\facade\Log;
use Throwable;

class UploadRealtimeMonitoringData
{
    public const ReturnCodeBusinessAbnormal = 400;
    public const ReturnCodeDatabaseAbnormal = 501;

    protected UploadRealtimeMonitoringDataRequest $request;
    protected SocketLogCollector $socketLogCollector;
    protected array $orderData;

    protected int $requestTime;

    public function __construct(UploadRealtimeMonitoringDataRequest $request, SocketLogCollector $socketLogCollector)
    {
        $this->request = $request;
        $this->socketLogCollector = $socketLogCollector;
        $this->requestTime = time();
    }

    public function run(): array
    {
        $this->socketLogCollector->collectorRunLog('上传实时监测数据=》' . json_encode_cn((array)$this->request));

        if ($this->request->transaction_serial_number === '00000000000000000000000000000000' || $this->request->status === 2) {
            return [];
        }

        // 加载订单数据
        $this->loadOrderData();
        // 订单状态检测
        $this->orderStatusDetection();
        // 推送Socket消息
        $this->pushSocketMessage();
        // 定时器处理
        $this->timerHandler();
        // 更新数据
        $this->updateDatabase();
        // 触发事件
        $this->triggerEvent();

        return [
            'corp_id' => $this->orderData['corp_id'],
            'station_id' => $this->orderData['station_id']
        ];
    }

    protected function loadOrderData(): void
    {
        try {
            $Order = new OrderModel();
            $orderData = $Order->where('id', $this->request->transaction_serial_number)
                ->field([
                    'id', 'user_id', 'status', 'piles_id', 'sequence', 'shot_id',
                    'corp_id', 'station_id', 'electricity_charged', 'period_codes',
                    'sharp_fee', 'sharp_ser_fee', 'peak_fee', 'peak_ser_fee',
                    'flat_fee', 'flat_ser_fee', 'valley_fee', 'valley_ser_fee',
                    'discount', 'surcharge', 'trans_start_time'
                ])
                ->find();
        } catch (DataNotFoundException|ModelNotFoundException|DbException $e) {
            $this->socketLogCollector->collect($e);
            throw new RuntimeException('数据库异常', self::ReturnCodeDatabaseAbnormal);
        }

        if (empty($orderData)) {
            $this->triggerShotsStatusChangeEvent();
            throw new RuntimeException('无效交易流水号', self::ReturnCodeBusinessAbnormal);
        }
        $this->orderData = $orderData->toArray();
    }

    protected function orderStatusDetection(): void
    {
        // 如果订单已经被强制结算了
        if (
            $this->orderData['status'] === OrderModel::StatusCompulsorySettlement ||
            $this->orderData['status'] === OrderModel::StatusAbnormal
        ) {
            // 让充电桩停止充电 引导走向不扣流程
            $context = new ChargingContextData([
                'token' => '',
                'user_id' => $this->orderData['user_id'],
                'type' => '小程序',
                'order_id' => $this->request->transaction_serial_number,
                'corp_id' => $this->orderData['corp_id'],
                'station_id' => $this->orderData['station_id'],
                'shot_id' => $this->orderData['shot_id']
            ]);
            $result = (new StopChargeCommand(
                new StopChargeCommandRequest([
                    'piles_id' => $this->request->piles_id,
                    'sequence' => (int)$this->orderData['sequence'],
                    'shot_id' => $this->orderData['shot_id'],
                    'context' => $context,
                    'operator' => EndOrder::OperatorSystem
                ])
            ))->send();
            $this->socketLogCollector->collectorRunLog(
                '检测订单已经被强制结算过了，现在发送停止充电请求，引导走向补扣流程。发送结果：' . json_encode_cn($result)
            );
        } else if (in_array($this->orderData['status'], [
            OrderModel::StatusComplete,
            OrderModel::StatusAbnormal,
            OrderModel::StatusOther
        ])) {
            throw new RuntimeException('订单已结束', self::ReturnCodeBusinessAbnormal);
        }
    }

    protected function pushSocketMessage(): void
    {
        $requestArray = (array)$this->request;
        $user_data = app(Order::class)->getOrderIdToContextMap($this->request->transaction_serial_number);
        // 发送Socket消息
        if (isset($user_data['type'])) {
            switch ($user_data['type']) {
                case '后台':
                    SendAdmin::send_group($user_data['token'], [
                        'type' => 'upload_realtime_monitoring_data',
                        'msg' => '上传实时监测数据',
                        'data' => $requestArray,
                    ]);
                    break;
                // 不使用websocket发送,由小程序轮询获取
                /*case '小程序':
                    SendApplet::send_uid($user_data['user_id'], [
                        'type' => 'upload_realtime_monitoring_data',
                        'msg' => '上传实时监测数据',
                        'data' => $requestArray
                    ]);
                    break;*/
            }
        } else {
            $this->socketLogCollector->collectorRunLog('print_r($user_data) = ' . print_r($user_data, true), $this->socketLogCollector::LevelWarning);
        }
    }

    protected function timerHandler(): void
    {
        // 删除定时器
        $redis = new Predis(config('my.redis2'));
        $timer_key = '运营平台远程控制启机' . $this->request->transaction_serial_number;
        if ($redis->hexists('SendHandleTimer', $timer_key)) {
            $res3 = send_http_post_timer([
                'type' => '删除-运营平台远程控制启机',
                'order_id' => $this->request->transaction_serial_number,
            ]);
            $this->socketLogCollector->collectorRunLog('send_http_post_timer：上传实时监测数据=》' . json_encode_cn($res3));
        }

        // 创建定时器
        $timer_key2 = '上传实时监测数据' . $this->request->transaction_serial_number;
        $res4 = send_http_post_timer([
            'type' => '上传实时监测数据',
            'order_id' => $this->request->transaction_serial_number,
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：上传实时监测数据=》' . $timer_key2 . '==>' . json_encode_cn($res4));

        // 自动结算
        $timer_key3 = '自动结算' . $this->request->transaction_serial_number;
        $res5 = send_http_post_timer([
            'type' => '自动结算',
            'order_id' => $this->request->transaction_serial_number,
        ]);
        $this->socketLogCollector->collectorRunLog('send_http_post_timer：上传实时监测数据=》' . $timer_key3 . '==>' . json_encode_cn($res5));
    }

    /**
     * 计算距离上次上报0x13又增加了多少电量
     *
     * @return int
     */
    protected function calculateElectricity(): int
    {
        return $this->request->charging_percentage - $this->orderData['electricity_charged'];
    }

    protected function updateDatabase(): void
    {
        $updateData = [
            'electricity_charged' => $this->request->charging_percentage
        ];

        // 计算与上一次上报的总电量的差额
        $electricity = $this->calculateElectricity();
        // 使用缓存来记录上次电量为0的时间戳
        $cacheKey = 'zero_electricity_' . $this->request->transaction_serial_number;
        if ($electricity === 0) {
            // 如果总电量小于1度视为刚开始充电,刚开始充电才需要发送微信异常通知提示用户
            if ($this->request->charging_percentage > 10000) {
                return;
            }

            $lastZeroTime = cache($cacheKey);
            $currentTime = time();
            if ($lastZeroTime === null) {
                // 第一次检测到电量为0，记录时间戳
                cache($cacheKey, $currentTime, 60); // 缓存1分钟
                return;
            }

            // 检查是否超过30秒
            if (($currentTime - $lastZeroTime) > 30) {
                // 超过则发送微信通知
                $notificationKey = 'notification_sent_' . $this->request->transaction_serial_number;
                if (!cache($notificationKey)) {
                    $this->sendWechatStartNotification();
                    cache($notificationKey, true, 3600); // 记录通知已发送，1小时内不再重复发送
                }
                return;
            }

            // 更新时间戳
            cache($cacheKey, $lastZeroTime, 60); // 保持原来的时间戳
            return;
        }
        $electricity_charged = $this->orderData['electricity_charged'];
        Log::info("上次上报电量: {$electricity_charged}, 本次上报电量: {$this->request->charging_percentage}, 电量差额: {$electricity}");
        // 电量差额异常
        if ($electricity < 0 || $electricity >= 10000) {
            Log::error('订单号：' . $this->request->transaction_serial_number . ',电量差额异常:' . $electricity);
            // 停止订单
            $context = new ChargingContextData([
                'token' => '',
                'user_id' => $this->orderData['user_id'],
                'type' => '小程序',
                'order_id' => $this->request->transaction_serial_number,
                'corp_id' => $this->orderData['corp_id'],
                'station_id' => $this->orderData['station_id']
            ]);

            (new StopChargeCommand(
                new StopChargeCommandRequest([
                    'piles_id' => $this->orderData['piles_id'],
                    'sequence' => $this->orderData['sequence'],
                    'shot_id' => $this->orderData['shot_id'],
                    'context' => $context
                ])
            ))->send();

            $this->orderData['id'] = $this->request->transaction_serial_number;
            $this->orderData['electricity_charged'] = $this->request->charging_percentage;
            $this->orderData['charge_duration'] = time() - strtotime($this->orderData['trans_start_time']);
            $this->orderData['msg'] = "上次上报电量: {$electricity_charged}, 本次上报电量: {$this->request->charging_percentage}, 电量差额: {$electricity}";
            $this->orderData['type'] = '电量差额异常';
            // 写入订单异常表
            Api::send("/order/orders/exception", 'POST', $this->orderData, ['Authorization' => 'mpQWnQ0zZ7KGl3JEV5DeUoeNXec0mohT']);
            // OrderException::saveOrder($this->orderData);
            return;
        }

        // 增加的电量(单位:度)
        $increaseElectricity = $electricity / 10000;

        $tariffType = self::getCurrentTariffType($this->orderData['period_codes'], $this->requestTime);
        switch ($tariffType) {
            case TariffGroup::PeriodTypeSharp:
                // (已充电量 / 10000) * (电价 / 100000) + ((已充电量 / 10000) * (服务价 / 100000) * 折扣)
                $electricityPrice = $this->orderData['sharp_fee'] / 100000;
                $servicePrice = $this->orderData['sharp_ser_fee'] / 100000;
                $updateData['sharp_electricity'] = Db::raw(sprintf('sharp_electricity+%d', $electricity));
                break;
            case TariffGroup::PeriodTypePeak:
                $electricityPrice = $this->orderData['peak_fee'] / 100000;
                $servicePrice = $this->orderData['peak_ser_fee'] / 100000;
                $updateData['peak_electricity'] = Db::raw(sprintf('peak_electricity+%d', $electricity));
                break;
            case TariffGroup::PeriodTypeFlat:
                $electricityPrice = $this->orderData['flat_fee'] / 100000;
                $servicePrice = $this->orderData['flat_ser_fee'] / 100000;
                $updateData['flat_electricity'] = Db::raw(sprintf('flat_electricity+%d', $electricity));
                break;
            case TariffGroup::PeriodTypeValley:
                $electricityPrice = $this->orderData['valley_fee'] / 100000;
                $servicePrice = $this->orderData['valley_ser_fee'] / 100000;
                $updateData['valley_electricity'] = Db::raw(sprintf('valley_electricity+%d', $electricity));
                break;
            default:
                $electricityPrice = $this->orderData['sharp_fee'] / 100000;
                $servicePrice = $this->orderData['sharp_ser_fee'] / 100000;
                $this->socketLogCollector->collectorRunLog('出现未知费率类型', SocketLogCollector::LevelError);
                break;
        }

        // 增加的充电费用(包括电费与服务费)(单位:元 | 精确到小数点后4位)
        // 计算公式 = ((增加电量 / 10000) * (电价 / 100000) + ((增加电量 / 10000) * (服务价 / 100000) * 折扣) + (增加电量 / 10000) * (附加费 / 100000)) * 10000
        // 简化公式 = (增加电量 * 电价) + (增加电量 * 服务价 * 折扣) + (增加电量 * 附加费)
        $surcharge = $this->orderData['surcharge'] / 100000;

        // 计算电费增加部分
        $increaseElectricityCost = $increaseElectricity * $electricityPrice;
        // 计算服务费增加部分(考虑折扣)
        $serviceFeeIncrease = $increaseElectricity * $servicePrice * ($this->orderData['discount'] / 100);
        // 计算附加费增加部分
        $surchargeIncrease = $increaseElectricity * $surcharge;

        // 计算总增加费用(防止附加费和服务费相加小于0)
        $increaseAmountCharged = (int)ceil(($increaseElectricityCost + max(0, $serviceFeeIncrease + $surchargeIncrease)) * 10000);

        $updateData['amount_charged'] = Db::raw(sprintf('amount_charged+%d', $increaseAmountCharged));

        // 检查 trans_start_time 是否存在且不为空
        if (isset($this->orderData['trans_start_time']) && $this->orderData['trans_start_time'] !== null) {
            // 如果 trans_start_time 是有效的,更新充电耗时
            $updateData['charge_duration'] = time() - strtotime($this->orderData['trans_start_time']);
        }

        // 更新订单: (已充电费用 和 已充电电量)
        $order_model = app(OrderModel::class);
        $order_model->where('id', $this->request->transaction_serial_number)->update($updateData);

        // 同步订单数据到订单微服务
        $orderData = $order_model->where('id', $this->request->transaction_serial_number)->find()->toArray();
        $orderData['discount'] = (float)$orderData['discount'];
        // 使用RabbitMQ同步订单数据到订单微服务
        Publisher::quickPublish([
            'exchange' => 'order',
            'type' => 'direct',
            'queues' => ['order_sync'],
            'routing_key' => 'order.sync',
            'message' => [
                'action' => 'update',
                'data' => $orderData,
            ]
        ]);

        // 更新用户的充电已使用金额 => 目前正在充电的订单已使用金额(保留4为小数)
        $amountCharged = $order_model->sumOrderTotalAmountCharged($this->orderData['user_id']);
        $amountChargedCent = $amountCharged / 100;
        $userModel = app(Users::class);
        $userModel->updateAmountCharged($this->orderData['user_id'], $amountCharged);
        cache('upload_realtime_monitoring_data_order_id' . $this->request->transaction_serial_number, array_merge((array)$this->request, ['amount_charged' => $amountCharged]), 3600 * 24);

        // 用户可用余额(实际余额 + 信用额度)
        $userAvailableBalance = $userModel->getUserAvailableBalance($this->orderData['user_id']);
        $this->socketLogCollector->collectorRunLog('用户可用余额(实际余额 + 信用额度) = ' . $userAvailableBalance);
        $this->socketLogCollector->collectorRunLog('用户的充电已使用金额 = ' . $amountChargedCent);
        // 如果 用户可用余额 小于等于 用户的充电已使用金额
        if ($userAvailableBalance <= $amountChargedCent) {
            // 停止该用户的所有进行中的充电任务
            $this->stopAllChargingOrder();
        }
    }

    protected function stopAllChargingOrder(): void
    {
        $order_model = app(OrderModel::class);
        $orderList = $order_model->getUserChargingOrders($this->orderData['user_id']);
        $this->socketLogCollector->collectorRunLog('查询用户进行中充电任务 = ' . json_encode_cn($orderList));
        if ($orderList === false) {
            // TODO: 查询数据库异常的处理
            $this->socketLogCollector->collectorRunLog('数据库查询失败', SocketLogCollector::LevelError);
        } else {
            foreach ($orderList as $value) {
                $context = new ChargingContextData([
                    'token' => '',
                    'user_id' => $this->orderData['user_id'],
                    'type' => '小程序',
                    'order_id' => $value['id'],
                    'corp_id' => $value['corp_id'],
                    'station_id' => $value['station_id']
                ]);

                (new StopChargeCommand(
                    new StopChargeCommandRequest([
                        'piles_id' => $value['piles_id'],
                        'sequence' => $value['sequence'],
                        'shot_id' => $value['shot_id'],
                        'context' => $context
                    ])
                ))->send();
            }
        }
    }

    protected function triggerShotsStatusChangeEvent(): void
    {
        // 查询充电枪数据
//        $shotId = (int)($this->request->piles_id * 100 + $this->request->shots_id);

        // 触发事件
        // TODO: 虽然目前这里是多此一举：上面都查询数据库了，将状态一起查询出来不就可以了，这里还查询缓存做什么？
        // TODO: 打算等梳理好 cache('充电桩服务订单_' . $order_id) 这块的变化后，再将上面数据库查询的数据换掉。
//        $ShotsStatusCache = app(ShotsStatusCache::class);
//        $beforeStatus = $ShotsStatusCache->getStatus($shotId);
//
//        if ($beforeStatus !== $this->request->status) {
//            $ShotsStatusCache->setStatus($shotId, $this->request->status);
//
//            try {
//                event('ShotsStatusChange', new ShotsStatusChangeEvent([
//                    'corp_id' => $this->orderData['corp_id'],
//                    'station_id' => $this->orderData['station_id'],
//                    'pile_id' => $this->orderData['piles_id'],
//                    'shot_id' => $shotId,
//                    'before_status' => $beforeStatus,
//                    'after_status' => $this->request->status
//                ]));
//            } catch (Throwable $e) {
//                // 在事件中运行的业务都是次要业务，若是出现异常情况，
//                // 只记录日志，不做其他处理，避免影响充电主流程。
//                $this->socketLogCollector->collect($e);
//            }
//        }
    }

    protected function getHardwareFailureArray(): array
    {
        $faults = [
            '急停按钮动作故障',
            '无可用整流模块',
            '出风口温度过高',
            '交流防雷故障',
            '交直流模块 DC20 通信中断',
            '绝缘检测模块 FC08 通信中断',
            '电度表通信中断',
            '读卡器通信中断',
            'RC10 通信中断',
            '风扇调速板故障',
            '直流熔断器故障',
            '高压接触器故障',
            '门打开'
        ];
        $hardware_failure = $this->request->hardware_failure;

        $fault_bin_count = strlen($hardware_failure);
        $hardware_failure_arr = [];

        for ($i = 0; $i < $fault_bin_count; $i++) {
            if ($hardware_failure[$i] == '1') {
                $hardware_failure_arr[] = $faults[$i] ?? '未知故障';
            }
        }
        return $hardware_failure_arr;
    }

    protected function triggerEvent(): void
    {
        $this->triggerShotsStatusChangeEvent();

        // 查询充电枪数据
        $shotId = (int)($this->request->piles_id . $this->request->shots_id);

        if (count($this->getHardwareFailureArray()) > 0) {
            try {
                event('DeviceAbnormality', new DeviceAbnormalityEvent([
                    'abnormality_type' => DeviceAbnormalityEvent::AbnormalityTypeUploadRealtimeMonitoringData,
                    'pile_id' => $this->orderData['piles_id'],
                    'shot_id' => $shotId,
                    'corp_id' => $this->orderData['corp_id'],
                    'station_id' => $this->orderData['station_id'],
                    'start_time' => $this->requestTime,
                    'abnormal_cause' => $this->request->hardware_failure
                ]));
            } catch (Throwable $e) {
                // 在事件中运行的业务都是次要业务，若是出现异常情况，
                // 只记录日志，不做其他处理，避免影响充电主流程。
                $this->socketLogCollector->collect($e);
            }
        }
    }

    public static function getCurrentTariffType(string $period_codes, int $time): string
    {
        $timeInterval = self::getCurrentTimeInterval($time);
        return substr($period_codes, ($timeInterval - 1) * 2, 2);
    }

    public static function getCurrentTimeInterval(int $time): int
    {
        $duration = $time - strtotime(date('Y-m-d 00:00:00', $time));
        return (int)ceil($duration / (30 * 60));
    }

    /**
     * 发送充电开始微信通知
     */
    protected function sendWechatStartNotification(): void
    {
        try {
            // 获取用户open_id
            $usersModel = app(Users::class);
            $userData = $usersModel->where('id', $this->orderData['user_id'])->field(['open_id'])->find();

            if (empty($userData) || empty($userData['open_id'])) {
                $this->socketLogCollector->collectorRunLog('发送充电开始微信通知失败：找不到用户open_id', SocketLogCollector::LevelError);
            }

            // 获取充电站信息
            $stationsModel = app(Stations::class);
            $stationData = $stationsModel->where('id', $this->orderData['station_id'])->field(['name'])->find();
            $stationName = $stationData ? $stationData['name'] : '未知站点';

            // 构建消息参数
            $params = [
                'thing2' => ['value' => '等待车辆同意充电中'],
                'character_string4' => ['value' => $this->orderData['shot_id']],
                'time3' => ['value' => date('Y-m-d H:i:s')],
                'thing1' => ['value' => $stationName],
                'thing5' => ['value' => '请关闭车端的预约充电功能']
            ];

            // 发送微信模板消息
            $wechat = app(WeChat::class);
            $result = $wechat->send_template_message($userData['open_id'], $this->orderData['id'], $params, 'exception');

            $this->socketLogCollector->collectorRunLog('发送充电开始微信通知结果：' . json_encode_cn($result));
        } catch (Throwable $e) {
            $this->socketLogCollector->collect($e);
        }
    }
}
